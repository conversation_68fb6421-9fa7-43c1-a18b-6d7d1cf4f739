import fs from "fs/promises";
import glob from "fast-glob";
import { join } from "path";

main();

async function main() {
  const uviewDir = join(__dirname, "../src/uni_modules/uview-ui");
  patch(uviewDir);
}

async function patch(uViewDir: string) {
  const componentsDir = join(uViewDir, "components");

  const files = await glob(["**/*.js", "**/*.vue"], {
    cwd: componentsDir,
  });

  for (const file of files) {
    const filePath = join(componentsDir, file);

    const src = await fs.readFile(filePath, { encoding: "utf-8" });

    if (!src.includes("uni.$u.")) {
      continue;
    }

    let modifiedSrc = src.replace(/uni\.\$u\./gm, "$u.");

    if (file.endsWith(".vue")) {
      const idx = modifiedSrc.indexOf("<script>");
      if (!idx) {
        console.log("failed to replace uni.$u:", file);
      } else {
        const s = ["<script>", `import $u from '../../libs';`].join("\n");

        modifiedSrc = modifiedSrc.replace("<script>", s);
      }
    } else {
      modifiedSrc = `import $u from '../../libs';\n` + modifiedSrc;
    }

    fs.writeFile(filePath, modifiedSrc);

    console.log("patch success:", file);
  }
}
