{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-weixin": "uni -p mp-weixin", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:mp-weixin": "uni build -p mp-weixin", "patch:uview-ui": "tsx scripts/patch-uview-ui.ts"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4070520250711001", "@dcloudio/uni-app-plus": "3.0.0-4070520250711001", "@dcloudio/uni-components": "3.0.0-4070520250711001", "@dcloudio/uni-h5": "3.0.0-4070520250711001", "@dcloudio/uni-mp-weixin": "3.0.0-4070520250711001", "dayjs": "^1.11.7", "vue": "^3.2.47"}, "devDependencies": {"@dcloudio/types": "^3.2.7", "@dcloudio/uni-automator": "3.0.0-4070520250711001", "@dcloudio/uni-cli-shared": "3.0.0-4070520250711001", "@dcloudio/uni-stacktracey": "3.0.0-4070520250711001", "@dcloudio/vite-plugin-uni": "3.0.0-4070520250711001", "@types/node": "^18.14.2", "@vue/tsconfig": "^0.1.3", "esbuild": "0.16.17", "fast-glob": "^3.2.12", "less": "^4.1.3", "sass": "^1.58.3", "tsx": "^3.12.3", "typescript": "^4.9.4", "unocss": "^0.50.1", "unocss-preset-weapp": "^0.4.2", "vite": "4.0.4"}}