// 看到此报错，是因为没有配置vue.config.js的【transpileDependencies】，详见：https://www.uviewui.com/components/npmSetting.html#_5-cli模式额外配置
const pleaseSetTranspileDependencies = {}, babelTest = pleaseSetTranspileDependencies?.test

import $u from './libs'

// $u挂载到uni对象上
uni.$u = $u

const install = (Vue) => {
    // 时间格式化，同时两个名称，date和timeFormat
    // Vue.filter('timeFormat', (timestamp, format) => uni.$u.timeFormat(timestamp, format))
    // Vue.filter('date', (timestamp, format) => uni.$u.timeFormat(timestamp, format))
    // 将多久以前的方法，注入到全局过滤器
    // Vue.filter('timeFrom', (timestamp, format) => uni.$u.timeFrom(timestamp, format))
    // 同时挂载到uni和Vue.prototype中
    // #ifndef APP-NVUE
    // 只有vue，挂载到Vue.prototype才有意义，因为nvue中全局Vue.prototype和Vue.mixin是无效的
    // Vue.prototype.$u = $u
    Vue.config.globalProperties.$u = $u;
    Vue.mixin($u.mixin)
    // #endif
}

export default {
    install
}
