// 引入全局mixin
import mixin from './mixin/mixin.js'
// 小程序特有的mixin
import mpMixin from './mixin/mpMixin.js'
// 全局挂载引入http相关请求拦截插件
import Request from './luch-request'

// 路由封装
import route from './util/route.js'
// 颜色渐变相关,colorGradient-颜色渐变,hexToRgb-十六进制颜色转rgb颜色,rgbToHex-rgb转十六进制
import colorGradient from './function/colorGradient.js'

// 规则检验
import test from './function/test.js'
// 防抖方法
import debounce from './function/debounce.js'
// 节流方法
import throttle from './function/throttle.js'
// 公共文件写入的方法
import index from './function/index.js'

// 配置信息
import config from './config/config.js'
// props配置信息
import props from './config/props.js'
// 各个需要fixed的地方的z-index配置文件
import zIndex from './config/zIndex.js'
// 关于颜色的配置，特殊场景使用
import color from './config/color.js'
// 平台
import platform from './function/platform'

const $u = {
    route,
    date: index.timeFormat, // 另名date
    colorGradient: colorGradient.colorGradient,
    hexToRgb: colorGradient.hexToRgb,
    rgbToHex: colorGradient.rgbToHex,
    colorToRgba: colorGradient.colorToRgba,
    test,
    type: ['primary', 'success', 'error', 'warning', 'info'],
    http: new Request(),
    config, // uView配置信息相关，比如版本号
    zIndex,
    debounce,
    throttle,
    mixin,
    mpMixin,
    props,
    ...index,
    color,
    platform
}

export default $u