import $u from '../../libs';
export default {
    props: {
        // 是否展示骨架组件
        loading: {
            type: <PERSON><PERSON>an,
            default: $u.props.skeleton.loading
        },
        // 是否开启动画效果
        animate: {
            type: <PERSON><PERSON><PERSON>,
            default: $u.props.skeleton.animate
        },
        // 段落占位图行数
        rows: {
            type: [String, Number],
            default: $u.props.skeleton.rows
        },
        // 段落占位图的宽度
        rowsWidth: {
            type: [String, Number, Array],
            default: $u.props.skeleton.rowsWidth
        },
        // 段落占位图的高度
        rowsHeight: {
            type: [String, Number, Array],
            default: $u.props.skeleton.rowsHeight
        },
        // 是否展示标题占位图
        title: {
            type: <PERSON><PERSON><PERSON>,
            default: $u.props.skeleton.title
        },
        // 段落标题的宽度
        titleWidth: {
            type: [String, Number],
            default: $u.props.skeleton.titleWidth
        },
        // 段落标题的高度
        titleHeight: {
            type: [String, Number],
            default: $u.props.skeleton.titleHeight
        },
        // 是否展示头像占位图
        avatar: {
            type: <PERSON><PERSON><PERSON>,
            default: $u.props.skeleton.avatar
        },
        // 头像占位图大小
        avatarSize: {
            type: [String, Number],
            default: $u.props.skeleton.avatarSize
        },
        // 头像占位图的形状，circle-圆形，square-方形
        avatarShape: {
            type: String,
            default: $u.props.skeleton.avatarShape
        }
    }
}
