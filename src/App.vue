<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";

onLaunch((opt) => {
  const accountInfo = uni.getAccountInfoSync();
  const _env = accountInfo.miniProgram.envVersion;
  uni.setStorageSync('MOBD_EVN_VERSION', _env);
});

onShow((opt) => {
  console.log("App Show");
});

onHide(() => {
  console.log("App Hide");
});
</script>

<style lang="less">
@import './style/common.less';
</style>
