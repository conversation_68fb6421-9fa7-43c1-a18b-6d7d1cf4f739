<template>
  <web-view
    v-if="httpUrl"
    :src="httpUrl"
    @message="handleWebViewMessage"
  ></web-view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getBaseByEnv } from "@/config";
import { onShow } from "@dcloudio/uni-app";

const httpUrl = ref("");
const webViewContext = ref<any>(null);

onShow(buildUrl);

function buildUrl() {
  const _env = uni.getStorageSync("MOBD_EVN_VERSION");

  let url = getBaseByEnv(_env === "release");

  const opt = uni.getEnterOptionsSync();

  const { path, ...other } = opt.query || {};

  if (path) {
    other._ts = Date.now()

    const query = Object.entries(other)
      .map(([key, value]) => `${key}=${value}`)
      .join("&");

    url += `#${path}?${query}`;
  }

  httpUrl.value = url;

  console.log('enter options', opt)
  console.log("build url:", httpUrl.value);
}

// 处理来自网页的消息
function handleWebViewMessage(event: any) {
  console.log('收到网页消息:', event.detail.data);

  const messages = event.detail.data;
  if (!messages || messages.length === 0) return;

  // 处理最新的消息
  const latestMessage = messages[messages.length - 1];

  switch (latestMessage.type) {
    case 'scanCode':
      handleScanCode(latestMessage);
      break;
    case 'getLocation':
      handleGetLocation(latestMessage);
      break;
    case 'chooseImage':
      handleChooseImage(latestMessage);
      break;
    case 'navigateTo':
      handleNavigateTo(latestMessage);
      break;
    default:
      console.log('未知消息类型:', latestMessage.type);
  }
}

// 处理扫码请求
function handleScanCode(message: any) {
  uni.scanCode({
    scanType: message.scanType || ['qrCode', 'barCode'],
    onlyFromCamera: message.onlyFromCamera || false,
    success: (result: any) => {
      // 向网页发送扫码结果
      postMessageToWebView({
        type: 'scanCodeResult',
        requestId: message.requestId,
        success: true,
        data: {
          result: result.result,
          scanType: result.scanType,
          charSet: result.charSet,
          path: result.path
        }
      });
    },
    fail: (error: any) => {
      console.error('扫码失败:', error);

      // 向网页发送错误信息
      postMessageToWebView({
        type: 'scanCodeResult',
        requestId: message.requestId,
        success: false,
        error: error
      });
    }
  });
}

// 处理获取位置请求
function handleGetLocation(message: any) {
  uni.getLocation({
    type: message.type || 'wgs84',
    altitude: message.altitude || false,
    success: (result: any) => {
      postMessageToWebView({
        type: 'getLocationResult',
        requestId: message.requestId,
        success: true,
        data: result
      });
    },
    fail: (error: any) => {
      console.error('获取位置失败:', error);

      postMessageToWebView({
        type: 'getLocationResult',
        requestId: message.requestId,
        success: false,
        error: error
      });
    }
  });
}

// 处理选择图片请求
async function handleChooseImage(message: any) {
  try {
    const result = await uni.chooseImage({
      count: message.count || 1,
      sizeType: message.sizeType || ['original', 'compressed'],
      sourceType: message.sourceType || ['album', 'camera']
    });

    postMessageToWebView({
      type: 'chooseImageResult',
      requestId: message.requestId,
      success: true,
      data: result
    });
  } catch (error) {
    console.error('选择图片失败:', error);

    postMessageToWebView({
      type: 'chooseImageResult',
      requestId: message.requestId,
      success: false,
      error: error
    });
  }
}

// 处理页面跳转请求
function handleNavigateTo(message: any) {
  try {
    if (message.url) {
      uni.navigateTo({
        url: message.url,
        success: () => {
          postMessageToWebView({
            type: 'navigateToResult',
            requestId: message.requestId,
            success: true
          });
        },
        fail: (error) => {
          postMessageToWebView({
            type: 'navigateToResult',
            requestId: message.requestId,
            success: false,
            error: error
          });
        }
      });
    }
  } catch (error) {
    console.error('页面跳转失败:', error);

    postMessageToWebView({
      type: 'navigateToResult',
      requestId: message.requestId,
      success: false,
      error: error
    });
  }
}

// 向网页发送消息
function postMessageToWebView(data: any) {
  // 注意：uni-app 中向 web-view 发送消息需要通过 web-view 的 context
  // 但是在当前版本中，主要是通过 URL 参数或者其他方式传递数据
  // 这里我们可以通过全局事件或者其他方式来实现
  console.log('向网页发送消息:', data);

  // 可以通过修改 URL 参数的方式传递消息
  // 或者使用 uni.postMessage (如果支持的话)
  try {
    // 尝试使用 postMessage (某些平台支持)
    if (webViewContext.value && webViewContext.value.postMessage) {
      webViewContext.value.postMessage({
        data: data
      });
    }
  } catch (error) {
    console.log('postMessage 不支持，使用其他方式');
    // 可以考虑通过 URL hash 或者其他方式传递消息
  }
}
</script>

<style></style>
