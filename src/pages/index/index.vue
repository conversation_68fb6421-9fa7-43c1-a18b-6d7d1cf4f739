<template>
  <web-view v-if="httpUrl" :src="httpUrl" @message="onMessage"
      @error="onWebviewError"
      @load="onWebviewLoad"></web-view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getBaseByEnv } from "@/config";
import { onShow } from "@dcloudio/uni-app";

const httpUrl = ref("");

onShow(buildUrl);

function buildUrl() {
  const _env = uni.getStorageSync("MOBD_EVN_VERSION");

  let url = getBaseByEnv(_env === "release");

  const opt = uni.getEnterOptionsSync();

  const { path, ...other } = opt.query || {};

  if (path) {
    other._ts = Date.now()

    const query = Object.entries(other)
      .map(([key, value]) => `${key}=${value}`)
      .join("&");

    url += `#${path}?${query}`;
  }

  httpUrl.value = url;

  console.log('enter options', opt)
  console.log("build url:", httpUrl.value);
}
</script>

<style></style>
