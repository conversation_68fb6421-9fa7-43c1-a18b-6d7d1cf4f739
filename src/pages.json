{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationStyle": "custom",
        "app-plus":{
          "titleNView":false
        }
			}
		}
	],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "智工育匠",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8",
    "navigationStyle": "custom",
    "app-plus":{
      "titleNView":false
    }
  },
  "uniIdRouter": {},
  "condition" : {//Mode configuration, effective only during development period.
    "current": 0,//Current active mode (index entry of list)
    "list": [
      {
        "name": "首页",//mode name
        "path": "pages/index/index",//startup page, can not be empty
        "query": ""//The startup parameters are obtained in the onLoad function of the page.
      }
    ]
  }
}
